#!/bin/bash

# Simple build script for local testing
# Usage: ./build-local.sh [tag]

set -e

TAG=${1:-"latest"}
IMAGE_NAME="adk-agent:${TAG}"

echo "🚀 Building Docker image: ${IMAGE_NAME}"

# Build the image
sudo docker build -t "${IMAGE_NAME}" .

echo "✅ Image built successfully!"

# Test the image locally
echo "🧪 Testing image locally..."
sudo docker run --rm -d --name test-adk-agent -p 10003:10002 \
  -e GOOGLE_API_KEY="${GOOGLE_API_KEY}" \
  "${IMAGE_NAME}"

# Wait for container to start
sleep 10

# Test health endpoint
if curl -f http://localhost:10003/.well-known/agent.json > /dev/null 2>&1; then
  echo "✅ Health check passed!"
  sudo docker stop test-adk-agent
  echo ""
  echo "🎉 Image ${IMAGE_NAME} is ready!"
  echo "To run the image:"
  echo "sudo docker run -p 10002:10002 -e GOOGLE_API_KEY=your_api_key ${IMAGE_NAME}"
else
  echo "❌ Health check failed!"
  sudo docker stop test-adk-agent
  exit 1
fi
