# ADK Agent - Google AI Agent Development Kit

A Python-based AI agent using Google's Agent Development Kit (ADK) that provides time-telling capabilities through a REST API.

## Features

- 🤖 AI-powered agent using Google Gemini
- 🌐 REST API with JSON-RPC 2.0 protocol
- 🐳 Docker containerized deployment
- 🔧 Easy configuration via environment variables

## Quick Start with Docker

### Prerequisites

- Docker installed
- Google API Key for Gemini
- Docker Hub account (for pushing images)

### 1. Build and Run Locally

```bash
# Quick build and test
./build-local.sh

# Or manually:
sudo docker build -t adk-agent .
sudo docker run -p 10002:10002 -e GOOGLE_API_KEY=your_api_key adk-agent
```

### 2. Using Docker Compose

```bash
# Make sure .env file contains GOOGLE_API_KEY
echo "GOOGLE_API_KEY=your_api_key" > .env

# Run with docker-compose
sudo docker-compose up -d
```

### 3. Build and Push to Docker Hub

```bash
# Use the provided script (will prompt for Docker Hub credentials)
./build-and-push.sh your-dockerhub-username adk-agent latest

# Or manually:
sudo docker build -t your-username/adk-agent:latest .
sudo docker login
sudo docker push your-username/adk-agent:latest
```

### 4. Pull and Run from Docker Hub

```bash
# Once pushed to Docker Hub, others can run:
sudo docker run -p 10002:10002 -e GOOGLE_API_KEY=your_api_key your-username/adk-agent:latest
```

## API Endpoints

- `GET /.well-known/agent.json` - Agent metadata
- `POST /` - Send tasks to the agent

## Environment Variables

- `GOOGLE_API_KEY` - Required. Your Google API key for Gemini

## Development

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the agent
python -m agents.google_adk --host 0.0.0.0 --port 10002
```

### Testing the Agent

```bash
# Test with the CLI client
python -m app.cmd --agent http://localhost:10002
```

## Docker Image Details

- Base image: `python:3.11-slim`
- Exposed port: `10002`
- Health check: Available at `/.well-known/agent.json`
- Non-root user for security

## Production Deployment

For production deployment, consider:

1. Using a reverse proxy (nginx, traefik)
2. Setting up proper logging
3. Using secrets management for API keys
4. Implementing monitoring and alerting
