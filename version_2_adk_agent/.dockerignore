# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.gitlab-ci.yml
.github/

# Documentation
README.md
*.md

# Logs
*.log

# Test files
tests/
test_*
*_test.py

# Environment files (will be provided at runtime)
.env.example

# Large files and caches
*.tar
*.zip
*.gz
node_modules/
.pytest_cache/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Build artifacts
build/
dist/
*.egg-info/

# Docker files (don't include in image)
Dockerfile*
docker-compose*
build-*.sh

.env
