#!/bin/bash

# Build and Push Script for ADK Agent Docker Image
# Usage: ./build-and-push.sh [your-dockerhub-username] [image-name] [tag]

set -e

# Default values
DOCKER_USERNAME=${1:-"your-username"}
IMAGE_NAME=${2:-"adk-agent"}
TAG=${3:-"latest"}

# Full image name
FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"

echo "🚀 Building Docker image: ${FULL_IMAGE_NAME}"

# Build the image
docker build -t "${FULL_IMAGE_NAME}" .

echo "✅ Image built successfully!"

# Test the image locally
echo "🧪 Testing image locally..."
docker run --rm -d --name test-adk-agent -p 10003:10002 \
  -e GOOGLE_API_KEY="${GOOGLE_API_KEY}" \
  "${FULL_IMAGE_NAME}"

# Wait for container to start
sleep 10

# Test health endpoint
if curl -f http://localhost:10003/.well-known/agent.json > /dev/null 2>&1; then
  echo "✅ Health check passed!"
  docker stop test-adk-agent
else
  echo "❌ Health check failed!"
  docker stop test-adk-agent
  exit 1
fi

# Login to Docker Hub (you'll be prompted for password)
echo "🔐 Logging in to Docker Hub..."
docker login

# Push the image
echo "📤 Pushing image to Docker Hub..."
docker push "${FULL_IMAGE_NAME}"

echo "🎉 Successfully pushed ${FULL_IMAGE_NAME} to Docker Hub!"
echo ""
echo "To run the image:"
echo "docker run -p 10002:10002 -e GOOGLE_API_KEY=your_api_key ${FULL_IMAGE_NAME}"
