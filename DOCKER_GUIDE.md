# Hướng dẫn Docker cho ADK Agent

## 📋 Tóm tắt các bướ<PERSON> để build và đẩy lên Docker Hub

### 🔧 Chuẩn bị

1. **Đảm bảo Docker đã được cài đặt và chạy**
2. **Có Google API Key** (đã có trong file `.env`)
3. **<PERSON><PERSON> tài khoản Docker Hub**

### 🚀 C<PERSON>c bước thực hiện

#### Bước 1: Build và test local
```bash
# Sử dụng script tự động
./build-local.sh

# Hoặc thủ công
sudo docker build -t adk-agent .
sudo docker run -p 10002:10002 -e GOOGLE_API_KEY="${GOOGLE_API_KEY}" adk-agent
```

#### Bước 2: Build và push lên Docker Hub
```bash
# Thay your-username bằng username Docker Hub của bạn
./build-and-push.sh your-username adk-agent latest
```

Script sẽ:
- Build image
- Test image locally
- Đ<PERSON><PERSON> nhập Docker Hub (nhập username/password)
- Push image lên Docker Hub

#### Bước 3: Verify trên Docker Hub
- Truy cập https://hub.docker.com/
- Kiểm tra repository `your-username/adk-agent`

### 🔍 Test image từ Docker Hub

```bash
# Pull và chạy image từ Docker Hub
sudo docker run -p 10002:10002 -e GOOGLE_API_KEY=your_api_key your-username/adk-agent:latest

# Test API
curl http://localhost:10002/.well-known/agent.json
```

### 📁 Các file quan trọng đã tạo

- `Dockerfile` - Định nghĩa cách build image
- `requirements.txt` - Dependencies Python
- `.dockerignore` - Loại trừ file không cần thiết
- `docker-compose.yml` - Chạy với docker-compose
- `build-local.sh` - Script build và test local
- `build-and-push.sh` - Script build và push lên Docker Hub

### 🛠️ Troubleshooting

**Lỗi permission denied:**
```bash
sudo usermod -aG docker $USER
# Sau đó logout và login lại
```

**Lỗi API key:**
- Kiểm tra file `.env` có chứa `GOOGLE_API_KEY`
- Đảm bảo API key hợp lệ

**Lỗi build:**
- Kiểm tra internet connection
- Thử build lại: `sudo docker build --no-cache -t adk-agent .`

### 📝 Lưu ý

- Image size khoảng 1-2GB do chứa nhiều dependencies
- Port mặc định: 10002
- Health check endpoint: `/.well-known/agent.json`
- Container chạy với non-root user để bảo mật
